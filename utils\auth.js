// 用户端token的key
const USER_TOKEN_NAME = "user_token";
// 代理端token的key
const PROXY_TOKEN_NAME = "proxy_token";
// 用户端userid的key
const USER_ID = "user_id";
// 代理端userid的key
const PROXY_USER_ID = "proxy_user_id";
// 用户端adminRabk的key
const USER_ADMIN_RANK = "user_admin_rank";
// 代理端adminRabk的key
const PROXY_ADMIN_RANK = "proxy_admin_rank";
// appversion的key
const APP_VERSION = "1.0.3";
// 代理端登录状态的key
const PROXY_LOGIN_STATUS = "proxy_login_status";
// 微信session的key
const WX_SESSION_KEY = "wx_session_key";
// 用户端登录时间戳的key
const USER_LOGIN_TIMESTAMP = "user_login_timestamp";
// 代理端登录时间戳的key
const PROXY_LOGIN_TIMESTAMP = "proxy_login_timestamp";
// 扫码登录标识的key（用于标记通过扫码完成的登录）
const SCAN_LOGIN_FLAG = "scan_login_flag";

// 用户端token管理
const setUserToken = function (data) {
  uni.setStorageSync(USER_TOKEN_NAME, data);
  setUserLoginTimestamp(Date.now());
};

const getUserToken = function () {
  return uni.getStorageSync(USER_TOKEN_NAME);
};

const removeUserToken = function () {
  uni.removeStorageSync(USER_TOKEN_NAME);
  removeUserLoginTimestamp();
};

// 代理端token管理
const setProxyToken = function (data) {
  uni.setStorageSync(PROXY_TOKEN_NAME, data);
  setProxyLoginTimestamp(Date.now());
};

const getProxyToken = function () {
  return uni.getStorageSync(PROXY_TOKEN_NAME);
};

const removeProxyToken = function () {
  console.log("[Auth] 移除代理端token");
  uni.removeStorageSync(PROXY_TOKEN_NAME);
  removeProxyLoginTimestamp();
};

// 兼容性函数 - 根据当前登录类型返回对应token
const setToken = function (data) {
  // 简化逻辑：根据当前代理端登录状态决定存储位置
  if (getProxyLoginStatus()) {
    setProxyToken(data);
  } else {
    setUserToken(data);
  }
};

const getToken = function () {
  // 安全的兼容性逻辑：只有在确实是代理端登录状态时才返回代理端token
  const proxyToken = getProxyToken();
  const proxyStatus = getProxyLoginStatus();
  const proxyUserId = getProxyUserId();

  // 只有同时满足以下条件才返回代理端token：
  // 1. 有代理端token
  // 2. 有代理端登录状态标识
  // 3. 有代理端userId
  if (proxyToken && proxyStatus === true && proxyUserId) {
    return proxyToken;
  }

  // 否则返回用户端token
  return getUserToken();
};

const removeToken = function () {
  removeUserToken();
  removeProxyToken();
};

// 用户端userId管理
const setUserId = function (data) {
  uni.setStorageSync(USER_ID, data);
};

const removeUserId = function () {
  uni.removeStorageSync(USER_ID);
};

// 代理端userId管理
const setProxyUserId = function (data) {
  uni.setStorageSync(PROXY_USER_ID, data);
};

const removeProxyUserId = function () {
  uni.removeStorageSync(PROXY_USER_ID);
};

// 用户端adminRabk管理
const setUserAdminRank = function (data) {
  uni.setStorageSync(USER_ADMIN_RANK, data);
};

const getUserAdminRank = function () {
  return uni.getStorageSync(USER_ADMIN_RANK) || "0";
};

const removeUserAdminRank = function () {
  uni.removeStorageSync(USER_ADMIN_RANK);
};

// 代理端adminRabk管理
const setProxyAdminRank = function (data) {
  uni.setStorageSync(PROXY_ADMIN_RANK, data);
};

const getProxyAdminRank = function () {
  return uni.getStorageSync(PROXY_ADMIN_RANK) || "0";
};

const removeProxyAdminRank = function () {
  uni.removeStorageSync(PROXY_ADMIN_RANK);
};

// 兼容性函数 - 根据当前登录状态返回对应的userId
const getUserId = function () {
  // 安全的兼容性逻辑：只有在确实是代理端登录状态时才返回代理端userId
  const proxyUserId = getProxyUserId();
  const proxyStatus = getProxyLoginStatus();
  const proxyToken = getProxyToken();

  // 只有同时满足以下条件才返回代理端userId：
  // 1. 有代理端userId
  // 2. 有代理端登录状态标识
  // 3. 有代理端token
  if (proxyUserId && proxyStatus === true && proxyToken) {
    return proxyUserId;
  }

  // 否则返回用户端userId
  return uni.getStorageSync(USER_ID);
};

// 专门获取用户端userId的函数
const getUserIdOnly = function () {
  return uni.getStorageSync(USER_ID);
};

const getProxyUserId = function () {
  return uni.getStorageSync(PROXY_USER_ID);
};

// 兼容性函数 - 根据当前登录类型返回对应userId
const getCurrentUserId = function () {
  // 优先返回代理端userId，如果没有则返回用户端userId
  const proxyUserId = getProxyUserId();
  if (proxyUserId) return proxyUserId;
  return getUserId();
};

// 用户端登录时间戳管理
const setUserLoginTimestamp = function (timestamp) {
  uni.setStorageSync(USER_LOGIN_TIMESTAMP, timestamp);
};

const getUserLoginTimestamp = function () {
  return uni.getStorageSync(USER_LOGIN_TIMESTAMP) || 0;
};

const removeUserLoginTimestamp = function () {
  uni.removeStorageSync(USER_LOGIN_TIMESTAMP);
};

// 代理端登录时间戳管理
const setProxyLoginTimestamp = function (timestamp) {
  uni.setStorageSync(PROXY_LOGIN_TIMESTAMP, timestamp);
};

const getProxyLoginTimestamp = function () {
  return uni.getStorageSync(PROXY_LOGIN_TIMESTAMP) || 0;
};

const removeProxyLoginTimestamp = function () {
  uni.removeStorageSync(PROXY_LOGIN_TIMESTAMP);
};

// 兼容性函数 - 设置登录时间戳
const setLoginTimestamp = function (timestamp) {
  // 根据当前登录状态设置对应的时间戳
  if (getProxyLoginStatus()) {
    setProxyLoginTimestamp(timestamp);
  } else {
    setUserLoginTimestamp(timestamp);
  }
};

// 兼容性函数 - 获取登录时间戳
const getLoginTimestamp = function () {
  // 优先返回代理端时间戳，如果没有则返回用户端时间戳
  const proxyTimestamp = getProxyLoginTimestamp();
  if (proxyTimestamp) return proxyTimestamp;
  return getUserLoginTimestamp();
};

// 兼容性函数 - 移除登录时间戳
const removeLoginTimestamp = function () {
  removeUserLoginTimestamp();
  removeProxyLoginTimestamp();
};

// 设置微信session key
const setWxSessionKey = function (sessionKey) {
  uni.setStorageSync(WX_SESSION_KEY, sessionKey);
};

// 获取微信session key
const getWxSessionKey = function () {
  return uni.getStorageSync(WX_SESSION_KEY);
};

// 移除微信session key
const removeWxSessionKey = function () {
  uni.removeStorageSync(WX_SESSION_KEY);
};

// 简化的登录状态过期检查
const isLoginExpired = function () {
  // 统一的过期检查逻辑
  const loginTime = getProxyLoginStatus() ? getProxyLoginTimestamp() : getUserLoginTimestamp();
  if (!loginTime) return true;

  // 24小时过期（可配置）
  const expireTime = 24 * 60 * 60 * 1000;
  return Date.now() - loginTime > expireTime;
};

// 检查微信session是否有效
const checkWxSession = function () {
  return new Promise((resolve, reject) => {
    // #ifdef MP-WEIXIN
    uni.checkSession({
      success: () => {
        console.log("[Auth] 微信session有效");
        resolve(true);
      },
      fail: () => {
        console.log("[Auth] 微信session已失效");
        resolve(false);
      },
    });
    // #endif

    // #ifndef MP-WEIXIN
    // 非微信小程序环境，直接返回true
    resolve(true);
    // #endif
  });
};

// 检查用户端登录状态
const isUserLogin = function () {
  const token = getUserToken();
  const userId = getUserIdOnly(); // 使用专门的用户端userId函数

  // 检查token和userId是否都存在
  const hasValidToken = token !== "" && token !== null && token !== undefined;
  const hasValidUserId =
    userId !== "" && userId !== null && userId !== undefined;

  return hasValidToken && hasValidUserId;
};

// 检查代理端登录状态
const isProxyLoginStrict = function () {
  const token = getProxyToken();
  const userId = getProxyUserId();
  const proxyStatus = getProxyLoginStatus();

  // 检查token、userId和代理状态是否都存在
  const hasValidToken = token !== "" && token !== null && token !== undefined;
  const hasValidUserId =
    userId !== "" && userId !== null && userId !== undefined;

  return hasValidToken && hasValidUserId && proxyStatus === true;
};

// 兼容性函数 - 综合检查登录状态
const isLogin = function () {
  // 优先检查代理端登录状态，如果没有则检查用户端登录状态
  return isProxyLoginStrict() || isUserLogin();
};

// 深度检查登录状态（包含session检查）
const checkLoginStatus = async function (options = {}) {
  try {
    // 1. 检查是否有token
    if (!isLogin()) {
      console.log("[Auth] 没有token，未登录");
      return { valid: false, reason: "no_token" };
    }

    // 2. 检查登录是否过期
    if (isLoginExpired()) {
      console.log("[Auth] 登录已过期");
      return { valid: false, reason: "expired" };
    }

    // 3. 检查微信session（仅在微信小程序中）
    // #ifdef MP-WEIXIN
    // 如果是扫码进入或应用启动场景，降低微信session检查的严格性
    const skipWxSessionCheck = options.skipWxSessionCheck || getScanLoginFlag();

    if (!skipWxSessionCheck) {
      const wxSessionValid = await checkWxSession();
      if (!wxSessionValid) {
        console.log("[Auth] 微信session失效，但保留登录状态");
        // 不再直接返回失败，而是标记需要刷新session
        return { valid: true, needRefreshSession: true };
      }
    } else {
      console.log("[Auth] 跳过微信session检查（扫码登录或应用启动场景）");
    }
    // #endif

    console.log("[Auth] 登录状态有效");
    return { valid: true };
  } catch (error) {
    console.error("[Auth] 检查登录状态出错:", error);
    return { valid: false, reason: "check_error" };
  }
};

// 设置代理端登录状态
const setProxyLoginStatus = function (status) {
  uni.setStorageSync(PROXY_LOGIN_STATUS, status);
};

// 获取代理端登录状态
const getProxyLoginStatus = function () {
  return uni.getStorageSync(PROXY_LOGIN_STATUS);
};

// 检查是否为代理端登录
const isProxyLogin = function () {
  return getProxyLoginStatus() === true && isLogin();
};

// 简化的代理端登录状态检查（用于页面刷新时的快速检查）
const isProxyLoginSimple = function () {
  const hasProxyToken = !!getProxyToken();
  const hasProxyUserId = !!getProxyUserId();
  const hasProxyStatus = !!getProxyLoginStatus();

  return hasProxyToken && hasProxyUserId && hasProxyStatus;
};

// 简化的登录状态检查（用于页面刷新时的快速检查）
const isLoginSimple = function () {
  const hasToken = !!getToken();
  const hasUserId = !!getUserId();

  return hasToken && hasUserId;
};

// 清除代理端登录状态
const clearProxyLoginStatus = function () {
  console.log("[Auth] 清除代理端登录状态标识");
  uni.removeStorageSync(PROXY_LOGIN_STATUS);
};

// 设置扫码登录标识
const setScanLoginFlag = function () {
  uni.setStorageSync(SCAN_LOGIN_FLAG, true);
  console.log("[Auth] 已设置扫码登录标识");
};

// 获取扫码登录标识
const getScanLoginFlag = function () {
  return uni.getStorageSync(SCAN_LOGIN_FLAG) === true;
};

// 清除扫码登录标识
const clearScanLoginFlag = function () {
  uni.removeStorageSync(SCAN_LOGIN_FLAG);
};

// 刷新登录状态（更新时间戳）
const refreshLoginStatus = function () {
  if (isLogin()) {
    setLoginTimestamp(Date.now());
    console.log("[Auth] 登录状态已刷新");
  }
};

// 重置用户端登录信息（保留邀请码）
const resetUserInfo = function () {
  const invitationCode = uni.getStorageSync("invitationCode");
  const invitationCodeType = uni.getStorageSync("invitationCodeType");

  // 清除用户端登录相关信息
  removeUserToken();
  removeUserId();
  removeUserAdminRank();
  removeWxSessionKey();

  // 恢复邀请码
  if (invitationCode) {
    uni.setStorageSync("invitationCode", invitationCode);
  }
  if (invitationCodeType) {
    uni.setStorageSync("invitationCodeType", invitationCodeType);
  }
  console.log("[Auth] resetUserInfo 完成，保留邀请码:", invitationCode);
};

// 重置代理端登录信息（保留邀请码）
const resetProxyInfo = function () {
  const invitationCode = uni.getStorageSync("invitationCode");
  const invitationCodeType = uni.getStorageSync("invitationCodeType");

  // 清除代理端登录相关信息
  removeProxyToken();
  removeProxyUserId();
  removeProxyAdminRank();
  clearProxyLoginStatus();
  removeWxSessionKey();

  // 恢复邀请码
  if (invitationCode) {
    uni.setStorageSync("invitationCode", invitationCode);
  }
  if (invitationCodeType) {
    uni.setStorageSync("invitationCodeType", invitationCodeType);
  }
  console.log("[Auth] resetProxyInfo 完成，保留邀请码:", invitationCode);
};

// 兼容性函数 - 重置所有登录信息（保留邀请码）
const resetInfo = function () {
  // 在清除所有存储前，先保存邀请码
  const invitationCode = uni.getStorageSync("invitationCode");
  const invitationCodeType = uni.getStorageSync("invitationCodeType");

  // 清除所有登录相关信息
  removeUserToken();
  removeProxyToken();
  removeUserId();
  removeProxyUserId();
  removeUserAdminRank();
  removeProxyAdminRank();
  removeWxSessionKey();
  clearProxyLoginStatus();
  clearScanLoginFlag(); // 清除扫码登录标识

  // 重新设置邀请码（如果存在）
  if (invitationCode) {
    uni.setStorageSync("invitationCode", invitationCode);
  }
  if (invitationCodeType) {
    uni.setStorageSync("invitationCodeType", invitationCodeType);
  }

  console.log("[Auth] resetInfo 完成，保留邀请码:", invitationCode);
};

export {
  // 兼容性函数
  setToken,
  getToken,
  removeToken,
  isLogin,
  isLoginSimple,
  resetInfo,
  setUserId,
  getUserId,
  getUserIdOnly,
  removeUserId,
  APP_VERSION,
  setProxyLoginStatus,
  getProxyLoginStatus,
  isProxyLogin,
  isProxyLoginSimple,
  clearProxyLoginStatus,
  setLoginTimestamp,
  getLoginTimestamp,
  removeLoginTimestamp,
  setWxSessionKey,
  getWxSessionKey,
  removeWxSessionKey,
  isLoginExpired,
  checkWxSession,
  checkLoginStatus,
  refreshLoginStatus,
  setScanLoginFlag,
  getScanLoginFlag,
  clearScanLoginFlag,
  // 新增的独立函数
  setUserToken,
  getUserToken,
  removeUserToken,
  setProxyToken,
  getProxyToken,
  removeProxyToken,
  setProxyUserId,
  getProxyUserId,
  removeProxyUserId,
  getCurrentUserId,
  isUserLogin,
  isProxyLoginStrict,
  resetUserInfo,
  resetProxyInfo,
  setUserLoginTimestamp,
  getUserLoginTimestamp,
  removeUserLoginTimestamp,
  setProxyLoginTimestamp,
  getProxyLoginTimestamp,
  removeProxyLoginTimestamp,
  // adminRabk 管理函数
  setUserAdminRank,
  getUserAdminRank,
  removeUserAdminRank,
  setProxyAdminRank,
  getProxyAdminRank,
  removeProxyAdminRank,
};
