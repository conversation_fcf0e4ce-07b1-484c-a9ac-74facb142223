import { showInformations } from "@/subpackages/proxy/api/proxyUser.js";
import { checkLoginStatus, isLoginSimple, setProxyAdminRank } from "@/utils/auth.js";

export default {
  namespaced: true,
  state: {
    userInfo: {
      adminRabk: "0", // 代理等级: "0"=普通用户, "1"=1级代理, "2"=2级代理, "3"=总代理
      headPic: "",
      phone: "",
      userName: "",
      integral: 0,
      name: "",
      realName: "",
      myInvitationCode: "",
    },
  },
  getters: {
    userInfo(state) {
      return state.userInfo;
    },
  },
  mutations: {
    SETUSERINFO(state, data) {
      state.userInfo = { ...data };
    },
    DEFAULTUSERINFO(state) {
      state.userInfo = {
        adminRabk: "0", // 重置为普通用户
        headPic: "",
        phone: "",
        userName: "",
        integral: 0,
        name: "",
        realName: "",
        myInvitationCode: "",
      };
    },
  },
  actions: {
    async getUserInfo({ commit }, { skipComplexCheck = false } = {}) {
      // 根据参数决定是否进行复杂的登录状态检查
      if (!skipComplexCheck) {
        // 先检查登录状态是否有效
        const loginStatus = await checkLoginStatus();
        if (!loginStatus.valid) {
          console.log(
            "[ProxyUser] 登录状态无效，无法获取用户信息:",
            loginStatus.reason
          );
          return Promise.reject(new Error("登录状态无效"));
        }
      } else {
        // 使用简化的登录状态检查
        if (!isLoginSimple()) {
          console.log("[ProxyUser] 简化检查：用户未登录");
          return Promise.reject(new Error("用户未登录"));
        }
      }

      try {
        const res = await showInformations();
        console.log("[ProxyUser] 获取用户信息成功:", res);
        if (res.data.code !== 200) {
          return Promise.reject(
            new Error(res.data.message || "获取用户信息失败")
          );
        }
        const userData = res.data.data;
        commit("SETUSERINFO", userData);

        // 同时将 adminRabk 保存到本地存储，确保重新编译后能正确恢复状态
        if (userData.adminRabk) {
          setProxyAdminRank(userData.adminRabk);
          console.log("[ProxyUser] 已将adminRabk保存到本地存储:", userData.adminRabk);
        }

        return userData;
      } catch (error) {
        console.error("[ProxyUser] 获取用户信息失败:", error);
        return Promise.reject(error);
      }
    },
  },
};
