import {
  checkLoginStatus,
  refreshLoginStatus,
  removeToken,
  removeUserId,
  clearProxyLoginStatus,
  removeWxSessionKey,
  getScanLoginFlag,
} from "./auth.js";
import store from "@/store";

/**
 * 登录状态管理器
 * 负责登录状态的检查、保活和自动恢复
 */
class SessionManager {
  constructor() {
    this.isChecking = false;
    this.lastCheckTime = 0;
  }

  /**
   * 初始化session管理器
   */
  init() {
    console.log("[SessionManager] 初始化登录状态管理器");
    // 简化初始化，只保留基本功能
  }

  /**
   * 检查并恢复登录状态
   */
  async checkAndRestoreLoginStatus(options = {}) {
    if (this.isChecking) return;

    this.isChecking = true;
    console.log("[SessionManager] 开始检查登录状态");

    try {
      // 在应用启动时跳过严格的微信session检查
      const checkOptions = {
        skipWxSessionCheck: options.isAppLaunch || false
      };

      const loginStatus = await checkLoginStatus(checkOptions);

      if (loginStatus.valid) {
        console.log("[SessionManager] 登录状态有效，刷新时间戳");
        refreshLoginStatus();

        // 恢复用户信息到store
        await this.restoreUserInfo();

        // 如果需要刷新session，异步处理
        if (loginStatus.needRefreshSession) {
          console.log("[SessionManager] 需要刷新微信session");
          this.refreshWxSessionAsync();
        }
        return { success: true, loginValid: true };
      } else {
        console.log("[SessionManager] 登录状态无效:", loginStatus.reason);
        await this.handleInvalidLogin(loginStatus.reason);
        return { success: true, loginValid: false, reason: loginStatus.reason };
      }
    } catch (error) {
      console.error("[SessionManager] 检查登录状态时出错:", error);
      return { success: false, error: error.message };
    } finally {
      this.isChecking = false;
      this.lastCheckTime = Date.now();
    }
  }

  /**
   * 恢复用户信息到store
   */
  async restoreUserInfo() {
    try {
      const { isProxyLoginStrict } = require("@/utils/auth.js");
      const proxyLoggedIn = isProxyLoginStrict();

      console.log("[SessionManager] 开始恢复用户信息，代理端登录状态:", proxyLoggedIn);

      // 根据登录状态恢复对应的用户信息
      if (proxyLoggedIn && store.state.proxyUser && store.dispatch) {
        try {
          console.log("[SessionManager] 恢复代理端用户信息...");
          await store.dispatch("proxyUser/getUserInfo", { skipComplexCheck: true });
          console.log("[SessionManager] 代理端用户信息恢复成功");
        } catch (error) {
          console.log("[SessionManager] 恢复代理端用户信息失败:", error.message);
        }
      } else if (store.state.user && store.dispatch) {
        try {
          console.log("[SessionManager] 恢复用户端用户信息...");
          await store.dispatch("user/getUserInfo");
          console.log("[SessionManager] 用户端用户信息恢复成功");
        } catch (error) {
          console.log("[SessionManager] 恢复用户端用户信息失败:", error.message);
        }
      }

      console.log("[SessionManager] 用户信息恢复尝试完成");
    } catch (error) {
      console.error("[SessionManager] 恢复用户信息过程出错:", error);
    }
  }

  /**
   * 处理无效登录状态
   */
  async handleInvalidLogin(reason) {
    console.log(`[SessionManager] 处理无效登录: ${reason}`);

    switch (reason) {
      case "wx_session_expired":
        // 微信session过期，需要重新登录
        await this.handleWxSessionExpired();
        break;

      case "expired":
        // 登录过期
        console.log("[SessionManager] 登录已过期，清除登录状态");
        this.clearLoginData();
        break;

      case "no_token":
        // 没有token，可能是首次使用
        console.log("[SessionManager] 未找到登录令牌");
        break;

      default:
        // 其他错误，清除登录状态
        console.log("[SessionManager] 登录状态检查出错，清除登录状态");
        this.clearLoginData();
        break;
    }
  }

  /**
   * 异步刷新微信session（不影响当前登录状态）
   */
  async refreshWxSessionAsync() {
    // #ifdef MP-WEIXIN
    try {
      console.log("[SessionManager] 异步刷新微信session");
      const loginRes = await uni.login();
      console.log("[SessionManager] 获取新的微信code:", loginRes.code);

      // 更新登录时间戳，表示session已刷新
      refreshLoginStatus();

      // TODO: 如果需要，可以调用后端接口更新session
      // await this.updateSessionWithNewCode(loginRes.code);
    } catch (error) {
      console.error("[SessionManager] 异步刷新微信session失败:", error);
      // 不清除登录数据，只是记录错误
    }
    // #endif
  }

  /**
   * 处理微信session过期
   */
  async handleWxSessionExpired() {
    console.log("[SessionManager] 处理微信session过期");

    // #ifdef MP-WEIXIN
    try {
      // 重新登录获取新的session
      const loginRes = await uni.login();
      console.log(
        "[SessionManager] 重新登录成功，获取新的code:",
        loginRes.code
      );

      // 这里需要调用您的后端接口，用新的code换取新的token
      // 暂时先刷新登录状态
      refreshLoginStatus();

      // TODO: 调用后端接口更新session
      // await this.updateSessionWithNewCode(loginRes.code);
    } catch (error) {
      console.error("[SessionManager] 重新登录失败:", error);

      // 如果是扫码登录，不清除登录数据，只记录错误
      if (getScanLoginFlag()) {
        console.log("[SessionManager] 扫码登录场景，保留登录状态");
        return;
      }

      // 非扫码登录才清除数据
      this.clearLoginData();
    }
    // #endif
  }

  /**
   * 清除登录数据
   */
  clearLoginData() {
    removeToken();
    removeUserId();
    clearProxyLoginStatus();
    removeWxSessionKey();

    // 清除store中的用户信息
    if (store.state.user) {
      store.commit("user/DEFAULTUSERINFO");
    }
    if (store.state.proxyUser) {
      store.commit("proxyUser/DEFAULTUSERINFO");
    }

    console.log("[SessionManager] 已清除所有登录数据");
  }

  /**
   * 手动刷新登录状态
   */
  async refresh() {
    console.log("[SessionManager] 手动刷新登录状态");
    await this.checkAndRestoreLoginStatus({ isAppLaunch: true });
  }
}

// 创建全局实例
const sessionManager = new SessionManager();

export default sessionManager;
