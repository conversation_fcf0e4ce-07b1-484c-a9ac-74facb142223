<style lang="scss">
/* 只引入必要的uView样式 */
/* 如果需要特定组件样式，可以按需引入 */
</style>
<script>
import { mapActions } from "vuex";
import { isLogin, isProxyLogin, APP_VERSION } from "@/utils/auth.js";
import { requestInterceptor } from "@/utils/requestInterceptor.js";
import { GetNewVersion } from "@/api/app.js";
import { wxlogin } from "@/api/login.js";
import { checkUserInvitation } from "@/api/user.js";
import { getDefaultHomePage } from "@/utils/routeGuard.js";
import sessionManager from "@/utils/sessionManager.js";
// 隔离测试工具已删除
export default {
  onLaunch: function (options) {
    // 初始化请求拦截器（全局只初始化一次）
    requestInterceptor();

    // 设置全局错误处理器
    this.setupGlobalErrorHandler();

    // 初始化微信环境
    this.initWeixinJSBridge();

    console.log("[App] onLaunch options:", options);
    console.log("[App] options.scene:", options.scene, "(这是小程序场景值，不是邀请码)");
    console.log("[App] options.query:", options.query);

    // 注意：App.vue的onLaunch中的options.scene是小程序场景值（如1001、1047等），不是邀请码
    // 真正的邀请码应该在页面的onLoad方法中通过options.scene获取
    console.log("[App] 邀请码将在页面onLoad中处理，不在App.vue中处理");

    // 初始化登录状态管理器
    this.initSessionManager();

    // 检查登录状态并跳转到正确页面
    this.checkLoginStatusAndRedirect();

    // 开发工具已删除

    // 检测app是否需要更新
    console.log("开始检查更新");
    this.checkForUpdates();
  },
  onShow: function (options) {
    console.log("App Show");
    console.log("[App] onShow options:", options);

    // 注意：onShow中的options.scene也是小程序场景值，不是邀请码
    // 邀请码处理应该在具体页面的onLoad中进行
  },
  onHide: function () {
    console.log("App Hide");
  },
  methods: {
    /**
     * 检查并处理小程序更新
     */
    checkForUpdates() {
      const updateManager = uni.getUpdateManager();

      // 检查更新
      updateManager.onCheckForUpdate((res) => {
        console.log("[App] 是否有新版本:", res.hasUpdate);
      });

      // 监听新版本下载成功
      updateManager.onUpdateReady(() => {
        this.promptForUpdate(updateManager);
      });

      // 监听新版本下载失败
      updateManager.onUpdateFailed(() => {
        console.error("[App] 新版本下载失败");
      });
    },

    /**
     * 提示用户应用新版本
     * @param {Object} updateManager - 更新管理对象
     */
    promptForUpdate(updateManager) {
      uni.showModal({
        title: "更新提示",
        content: "新版本已准备好，是否重启应用？",
        showCancel: false,
        success: (res) => {
          if (res.confirm) {
            console.log("[App] 用户确认重启应用");
            updateManager.applyUpdate();
          }
        },
      });
    },

    /* toLogin() {
        console.log("调用登录")
        let that = this
        uni.login({
          "provider": "weixin",
          "onlyAuthorize": true, // 微信登录仅请求授权认证
          success: function(event) {
            const {
              code
            } = event
            console.log(event)
            //客户端成功获取授权临时票据（code）,向业务服务器发起登录请求。
            let data = {
              code: event.code
            }
            wxlogin(data).then(res => {
              console.log("登录返回信息")
              console.log(res)
              uni.setStorageSync("token", res.data.data.token)
              uni.setStorageSync("userId", res.data.data.userId)
              uni.setStorageSync("phone", res.data.data.phone)
              let invitation = uni.getStorageSync("invitationCode")
              if (invitation == null || invitation == undefined || invitation == "") {

              } else {
                // 调用接口，获取用户信息，并绑定code
                let useData = {
                  userId: res.data.data.userId,
                  invitation: invitation
                }

                console.log("开始发送检查请求")
                checkUserInvitation(useData).then(res => {
                  console.log(res)

                })
              }
            })
          },
          fail: function(err) {
            console.log(err)
            // 登录授权失败
            // err.code是错误码
          }
        })

      }, */

    initWeixinJSBridge() {
      // 检查是否在微信环境中
      // #ifdef MP-WEIXIN
      // 微信小程序环境，不需要WeixinJSBridge
      console.log("微信小程序环境，跳过WeixinJSBridge初始化");
      return;
      // #endif

      // #ifdef H5
      // H5环境才需要WeixinJSBridge
      try {
        if (typeof window !== "undefined" && window.WeixinJSBridge) {
          // 已经存在，直接初始化
          this.initBridge();
        } else {
          // 监听WeixinJSBridgeReady事件
          if (typeof document !== "undefined") {
            document.addEventListener(
              "WeixinJSBridgeReady",
              () => {
                this.initBridge();
              },
              false
            );

            // 添加超时处理，避免无限等待
            setTimeout(() => {
              if (!window.WeixinJSBridge) {
                console.warn("WeixinJSBridge 初始化超时，可能不在微信环境中");
              }
            }, 5000);
          }
        }
      } catch (err) {
        console.error("WeixinJSBridge 环境检测失败:", err);
      }
      // #endif

      // #ifdef APP-PLUS
      // App环境不需要WeixinJSBridge
      console.log("App环境，跳过WeixinJSBridge初始化");
      // #endif
    },

    initBridge() {
      try {
        if (
          typeof window !== "undefined" &&
          window.WeixinJSBridge &&
          typeof window.WeixinJSBridge.invoke === "function"
        ) {
          // 初始化成功
          console.log("WeixinJSBridge 初始化成功");
          // 可以在这里添加微信支付等功能的初始化
          this.setupWeixinPayment();
        } else {
          console.warn("WeixinJSBridge 不可用");
        }
      } catch (err) {
        console.error("WeixinJSBridge 初始化失败:", err);
      }
    },

    setupWeixinPayment() {
      // 微信支付相关初始化
      try {
        // 这里可以添加微信支付的初始化逻辑
        console.log("微信支付环境准备就绪");
      } catch (err) {
        console.error("微信支付初始化失败:", err);
      }
    },

    /**
     * 初始化登录状态管理器
     */
    initSessionManager() {
      try {
        console.log("[App] 初始化登录状态管理器");
        sessionManager.init();
      } catch (error) {
        console.error("[App] 初始化登录状态管理器失败:", error);
      }
    },

    /**
     * 检查登录状态并重定向到正确页面
     */
    checkLoginStatusAndRedirect() {
      // 延迟执行，确保sessionManager初始化完成
      setTimeout(async () => {
        const currentPages = getCurrentPages();
        const currentPath =
          currentPages.length > 0
            ? "/" + currentPages[currentPages.length - 1].route
            : "";

        console.log("[App] 当前页面:", currentPath);
        console.log("[App] 登录状态:", {
          isLogin: isLogin(),
          isProxy: isProxyLogin(),
        });

        // 让sessionManager先检查并恢复登录状态（应用启动时跳过严格的微信session检查）
        const restoreResult = await sessionManager.checkAndRestoreLoginStatus({ isAppLaunch: true });

        console.log("[App] 登录状态恢复结果:", restoreResult);

        // 只有在以下情况才进行页面跳转：
        // 1. 当前页面是统一登录页面，但用户已登录
        // 2. 当前页面为空（首次启动）
        // 3. 当前页面需要登录但用户未登录
        const needRedirect = this.shouldRedirectOnLaunch(currentPath);

        if (needRedirect) {
          // 等待一小段时间确保store状态完全恢复
          await new Promise(resolve => setTimeout(resolve, 100));

          const defaultPage = getDefaultHomePage();
          console.log("[App] 需要跳转到默认页面:", defaultPage);

          // 再次检查登录状态，确保跳转逻辑正确
          const finalAdminRank = this.getFinalAdminRank();
          const finalIsProxy = isProxyLogin();

          console.log("[App] 最终状态检查:", {
            adminRank: finalAdminRank,
            isProxy: finalIsProxy,
            defaultPage: defaultPage
          });

          uni.reLaunch({
            url: defaultPage,
          });
        } else {
          console.log("[App] 保持在当前页面:", currentPath);
        }
      }, 200);
    },

    /**
     * 获取最终的adminRank（确保store已恢复）
     */
    getFinalAdminRank() {
      try {
        const { isProxyLoginStrict } = require("@/utils/auth.js");
        const proxyLoggedIn = isProxyLoginStrict();

        if (proxyLoggedIn) {
          // 直接从store获取代理端用户信息
          const proxyUserInfo = this.$store.getters.proxyUserInfo;
          if (proxyUserInfo && proxyUserInfo.adminRabk) {
            console.log("[App] 从代理端store获取adminRank:", proxyUserInfo.adminRabk);
            return proxyUserInfo.adminRabk;
          }
        }

        // 从用户端store获取
        const userInfo = this.$store.getters.userInfo;
        if (userInfo && userInfo.adminRabk) {
          console.log("[App] 从用户端store获取adminRank:", userInfo.adminRabk);
          return userInfo.adminRabk;
        }

        console.log("[App] 无法获取adminRank，返回默认值0");
        return "0";
      } catch (error) {
        console.warn("[App] 获取adminRank失败:", error);
        return "0";
      }
    },

    /**
     * 判断是否需要在应用启动时进行页面重定向
     */
    shouldRedirectOnLaunch(currentPath) {
      // 修改：首次启动时不再强制跳转，让用户直接进入首页
      // if (!currentPath) {
      //   return true;
      // }

      // 如果在统一登录页面但已经登录，需要跳转到首页
      if (currentPath === "/pages/unifiedLogin/unifiedLogin" && isLogin()) {
        return true;
      }

      // 检查当前页面是否需要登录权限
      const { routeGuard } = require("@/utils/routeGuard.js");
      const guardResult = routeGuard(currentPath);

      // 如果当前页面需要登录但用户未登录，需要跳转到登录页
      if (!guardResult.canAccess) {
        return true;
      }

      // 其他情况保持在当前页面
      return false;
    },

    /**
     * 设置全局错误处理器
     */
    setupGlobalErrorHandler() {
      // 处理未捕获的Promise拒绝
      // #ifdef MP-WEIXIN
      wx.onUnhandledRejection((event) => {
        const error = event.reason;

        // 如果是用户取消操作（如取消选择地址），不显示错误
        if (error && error.errMsg) {
          if (
            error.errMsg.includes("cancel") ||
            error.errMsg.includes("Cancel") ||
            error.errMsg.includes("用户取消")
          ) {
            console.log("用户取消操作，忽略错误:", error.errMsg);
            return;
          }
        }

        // 其他错误正常处理
        console.error("未处理的Promise拒绝:", error);
      });

      // 监听页面错误
      wx.onError((error) => {
        console.error("页面错误:", error);
      });
      // #endif

      // #ifdef H5
      // H5环境的错误处理
      if (typeof window !== "undefined") {
        window.addEventListener("unhandledrejection", (event) => {
          const error = event.reason;

          if (error && error.errMsg && error.errMsg.includes("cancel")) {
            console.log("用户取消操作，忽略错误:", error.errMsg);
            event.preventDefault(); // 阻止默认错误处理
            return;
          }

          console.error("未处理的Promise拒绝:", error);
        });
      }
      // #endif
    },
  },
};
</script>

<style>
/*每个页面公共css */
.wd {
  margin-left: 10px;
  margin-right: 10px;
}

.bkcolor {
  background-color: #ededed;
  min-height: 110vh;
  overflow: hidden;
}

uni-toast,
uni-modal {
  z-index: 99999999;
}
</style>
