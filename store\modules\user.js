import { showInformations } from "@/api/user.js";
import { isLogin, setUserAdminRank } from "@/utils/auth.js";

export default {
  namespaced: true,
  state: {
    userInfo: {
      adminRabk: "0", // 代理等级: "0"=普通用户, "1"=1级代理, "2"=2级代理, "3"=总代理
      headPic: "",
      phone: "",
      userName: "",
      integral: 0,
    },
  },
  getters: {},
  mutations: {
    SETUSERINFO(state, data) {
      state.userInfo = data
        ? { ...data }
        : {
            adminRabk: "0",
            headPic: "",
            phone: "",
            userName: "",
            integral: 0,
          };
    },
    DEFAULTUSERINFO(state) {
      state.userInfo = {
        adminRabk: "0",
        headPic: "",
        phone: "",
        userName: "",
        integral: 0,
      };
    },
  },
  actions: {
    async getUserInfo({ commit }) {
      if (!isLogin()) {
        return Promise.reject(new Error("用户未登录"));
      }

      try {
        const res = await showInformations();
        console.log("[User] 获取用户信息成功:", res);
        if (res.data.code !== 200) {
          return Promise.reject(
            new Error(res.data.message || "获取用户信息失败")
          );
        }
        const userData = res.data.data;
        commit("SETUSERINFO", userData);

        // 同时将 adminRabk 保存到本地存储，确保重新编译后能正确恢复状态
        if (userData.adminRabk) {
          setUserAdminRank(userData.adminRabk);
          console.log("[User] 已将adminRabk保存到本地存储:", userData.adminRabk);
        }

        return userData;
      } catch (error) {
        console.error("[User] 获取用户信息失败:", error);
        return Promise.reject(error);
      }
    },
  },
};
